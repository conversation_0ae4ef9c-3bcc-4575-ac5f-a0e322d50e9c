<!-- 能源总览 -->
<template>
  <CPanel>
    <template #header>能源总览</template>
    <template #content>
      <div class="energy-overview">
        <!-- 上半部分：圆环图和系统列表 -->
        <div class="energy-summary">
          <!-- 左侧圆环图 -->
          <div class="energy-chart">
            <CEcharts :option="pieOption" />
            <div class="chart-center">
              <div class="center-number">3</div>
              <div class="center-text">能耗总类</div>
            </div>
          </div>

          <!-- 右侧系统列表 -->
          <div class="system-list">
            <div class="system-item" v-for="item in systemData" :key="item.name">
              <div class="system-indicator" :style="{ backgroundColor: item.color }"></div>
              <span class="system-name">{{ item.name }}</span>
              <div class="system-value-container">
                <span class="system-value">{{ item.value }}%</span>
                <div v-if="item.badge" class="system-badge">{{ item.badge }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 下半部分：能耗趋势图 -->
        <div class="energy-trend">
          <!-- 背景图片容器 -->
          <div class="trend-background">
            <!-- 标题和选择器覆盖在背景图上 -->
            <div class="trend-header">
              <div class="trend-title">
                <span>能耗趋势</span>
              </div>
              <div class="trend-selector">
                <select class="time-selector">
                  <option value="day">日</option>
                  <option value="week">周</option>
                  <option value="month">月</option>
                </select>
              </div>
            </div>
            <!-- 图表区域 -->
            <div class="trend-chart">
              <CEcharts :option="trendOption" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'
import { ref, onMounted } from 'vue'

// 系统数据
const systemData = ref([
  { name: '光伏系统', value: 45, color: '#00D4AA', badge: null },
  { name: '电力系统', value: 23, color: '#FFB800', badge: '208×20' },
  { name: '地源热泵系统', value: 16, color: '#00B4FF', badge: null }
])

// 圆环图配置
const pieOption = ref<any>({})

// 趋势图配置
const trendOption = ref<any>({})

// 初始化圆环图
const initPieChart = () => {
  return {
    tooltip: {
      show: false
    },
    legend: {
      show: false
    },
    series: [
      {
        type: 'pie',
        radius: ['60%', '80%'],
        center: ['50%', '50%'],
        data: [
          { value: 45, name: '光伏系统', itemStyle: { color: '#00D4AA' } },
          { value: 23, name: '电力系统', itemStyle: { color: '#FFB800' } },
          { value: 16, name: '地源热泵系统', itemStyle: { color: '#00B4FF' } },
          { value: 16, name: '其他', itemStyle: { color: '#4A5568' } }
        ],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        emphasis: {
          disabled: true
        }
      }
    ]
  }
}

// 初始化趋势图
const initTrendChart = () => {
  const hours = ['00:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '18:00', '20:00']
  const energyData = [60, 58, 55, 50, 45, 40, 35, 30, 25]
  const carbonData = [35, 36, 37, 38, 39, 38, 37, 35, 20]

  return {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00D4AA',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      data: ['能耗', '碳排'],
      right: '10%',
      top: '5%',
      textStyle: {
        color: '#C5D6E6'
      },
      itemWidth: 15,
      itemHeight: 2
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: hours,
      axisLabel: {
        color: '#C5D6E6',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(76, 93, 130, 0.5)'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        min: 0,
        max: 60,
        axisLabel: {
          color: '#C5D6E6',
          fontSize: 12
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(76, 93, 130, 0.3)',
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        min: 0,
        max: 60,
        axisLabel: {
          color: '#C5D6E6',
          fontSize: 12
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '能耗',
        type: 'line',
        data: energyData,
        smooth: true,
        lineStyle: {
          color: '#00D4AA',
          width: 3
        },
        itemStyle: {
          color: '#00D4AA'
        },
        symbol: 'none'
      },
      {
        name: '碳排',
        type: 'line',
        yAxisIndex: 1,
        data: carbonData,
        smooth: true,
        lineStyle: {
          color: '#FFB800',
          width: 3
        },
        itemStyle: {
          color: '#FFB800'
        },
        symbol: 'none'
      }
    ]
  }
}

onMounted(() => {
  pieOption.value = initPieChart()
  trendOption.value = initTrendChart()
})
</script>

<style lang="scss" scoped>
.energy-overview {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: url('@/assets/img/ny_bg.png') no-repeat center center;
  background-size: 100% 100%;
}

.energy-summary {
  display: flex;
  align-items: center;
  gap: 20px;
  height: 180px;
}

.energy-chart {
  position: relative;
  width: 160px;
  height: 160px;
  flex-shrink: 0;
}

.chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;

  .center-number {
    font-size: 36px;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 4px;
  }

  .center-text {
    font-size: 14px;
    color: #C5D6E6;
  }
}

.system-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.system-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  border-left: 3px solid transparent;

  &:nth-child(1) {
    border-left-color: #00D4AA;
  }

  &:nth-child(2) {
    border-left-color: #FFB800;
  }

  &:nth-child(3) {
    border-left-color: #00B4FF;
  }
}

.system-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.system-name {
  flex: 1;
  color: #C5D6E6;
  font-size: 14px;
}

.system-value-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.system-value {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
}

.system-badge {
  background: url('@/assets/img/circle_bg.svg') no-repeat center center;
  background-size: 100% 100%;
  padding: 2px 8px;
  font-size: 12px;
  color: #00D4AA;
  border-radius: 10px;
}

.energy-trend {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.trend-background {
  position: relative;
  flex: 1;
  background: url('@/assets/img/n_samllTitle.png') no-repeat center top;
  background-size: 100% auto;
  display: flex;
  flex-direction: column;
}

.trend-header {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.trend-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}

.trend-icon {
  color: #00D4AA;
  font-size: 12px;
}

.trend-selector {
  .time-selector {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: #C5D6E6;
    padding: 4px 12px;
    font-size: 12px;
    outline: none;
    cursor: pointer;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
    }

    option {
      background: #1a1a1a;
      color: #C5D6E6;
    }
  }
}

.trend-chart {
  flex: 1;
  min-height: 200px;
  padding-top: 50px; /* 为标题留出空间 */
  padding: 50px 10px 10px 10px;
}
</style>
